# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a modern browser extension project built with the WXT framework (https://wxt.dev/). The repository contains two extension projects:

1. **fwyy-tools** - A comprehensive TypeScript-based browser extension tool collection

Focus development efforts on the **fwyy-tools** project, which is the main modern extension.

## Development Commands

### Main Commands (fwyy-tools)
```bash
# Development
npm run dev              # Chrome development mode with hot reload
npm run dev:firefox      # Firefox development mode

# Building
npm run build           # Build Chrome version for production
npm run build:firefox   # Build Firefox version for production

# Packaging
npm run zip             # Package Chrome extension as .zip
npm run zip:firefox     # Package Firefox extension as .zip

# Type checking
npm run compile         # TypeScript compilation check

# Tool generation
npm run create-tool     # Generate new tool template
npm run tool:create     # Alias for create-tool
```

### Setup Commands
```bash
npm install            # Install dependencies
npm run setup-styles   # Setup style symlinks (runs automatically)
```

## Architecture Overview

### Core Directory Structure
- `entrypoints/` - Extension entry points (background, content, popup)
- `utils/` - Core utility systems and managers
- `tools/` - Individual tool implementations
- `styles/` - Design system and CSS files
- `scripts/` - Development scripts
- `docs/` - Architecture documentation

### Key Systems

#### Tool System
- **BaseTool** (`utils/tool-template.ts`) - Base class for all tools
- **ToolRegistry** (`utils/tool-registry.ts`) - Tool registration and management
- **CategoryManager** (`utils/category-manager.ts`) - Dynamic category system

#### UI System
- **UIComponents** (`utils/ui-components.ts`) - Reusable UI components
- **StyleManager** (`utils/style-manager.ts`) - CSS module management
- **NotificationManager** (`utils/notification-manager.ts`) - Notification system

#### Data Management
- **SettingsManager** (`utils/settings-manager.ts`) - User settings
- **VersionManager** (`utils/version-manager.ts`) - Version control and updates

### Extension Architecture
- **Background Script** (`entrypoints/background.ts`) - Extension lifecycle management
- **Content Script** (`entrypoints/content.ts`) - Page interaction (scoped to *.zuoyebang.cc/*)
- **Popup** (`entrypoints/popup/`) - Main user interface

## Adding New Tools

### Using Tool Generator (Recommended)
```bash
npm run create-tool
```

### Manual Tool Creation
1. Create tool class extending `BaseTool`
2. Implement required properties: `id`, `name`, `description`, `icon`, `categories`, `version`
3. Implement `action()` method with tool functionality
4. Register tool in `entrypoints/popup/main.ts`

### Tool Template Example
```typescript
import { BaseTool } from '@/utils/tool-template';

export class MyTool extends BaseTool {
  id = 'my-tool';
  name = 'My Tool';
  description = 'Tool description';
  icon = '🛠️';
  categories = ['all', 'utility'];
  version = { major: 1, minor: 0, patch: 0 };

  async action(): Promise<void> {
    // Tool implementation
  }
}
```

## Key Configuration Files

- **wxt.config.ts** - WXT framework configuration and extension manifest
- **tsconfig.json** - TypeScript configuration (extends WXT defaults)
- **package.json** - Dependencies and scripts
- **web-ext.config.ts** - Web extension configuration

## Extension Permissions

The extension uses these key permissions:
- `activeTab`, `storage`, `tabs`, `scripting` - Core functionality
- `notifications`, `downloads`, `clipboardWrite` - User interaction
- `cookies`, `webRequest`, `clipboardRead` - Data access
- Host permissions for `<all_urls>` and specific domains

## Style System

Uses a design token system with modular CSS:
- `styles/design-tokens.css` - Color, spacing, typography tokens
- `styles/components.css` - Component styles
- `styles/layout.css` - Layout styles
- `styles/utilities.css` - Utility classes

## Development Notes

- Project uses TypeScript with strict typing
- WXT framework provides hot reload and modern build tooling
- Extension supports both Chrome and Firefox
- All data is stored locally using Chrome storage APIs
- UI follows modern design patterns with responsive layout

## Important Files to Review

- `entrypoints/popup/main.ts` - Main application logic and tool management
- `utils/tool-template.ts` - Tool interface and base class
- `utils/tool-registry.ts` - Tool registration system
- `wxt.config.ts` - Extension configuration and permissions
- `README.md` - Comprehensive project documentation